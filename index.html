<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
</head>
<style>
    html, body {
        height: 100%;
        margin: 0;
    }
    main {
        background-color: #e1e1e1;
        display: grid; 
        place-items: center;
        grid-template-columns: repeat(1, 1fr);
        position: relative;
        height: 100%;
    }
    section {
        background-color: #fff;
        border-radius: 50%;
        padding: 20px;
        position: absolute;
        overflow: hidden;
        cursor: pointer;
        transition: z-index 0.3s ease, transform 0.3s ease;

        display: flex;
        justify-content: center;
        align-items: center;
    }
    #cover {
        display: flex;
        
    }
     section:nth-child(4) {
        width: 400px;
        height: 400px;
        z-index: 1;
        border: 5px solid red;
     }
     section:nth-child(3) {
        width: 300px;
        height: 300px;
        z-index: 2;
        border: 5px solid blue;
     }
     section:nth-child(2) {
        width: 200px;
        height: 200px;
        z-index: 3;
        border: 5px solid green;
     }
     section:nth-child(1) {
        width: 1000px;
        height: 1000px;
        z-index: 4;
        border: 5px solid purple;
     }
     section:hover {
        transform: scale(1.1);
     }
    .elevate {
        z-index: 10;
    } 

    
</style>
<body>
    <main>
        <section>
          <div id="cover">
            <div>
              <h1>Main Title</h1>    
              <p>Lorem ipsum dolor sit amet consectetur adipisicing elit. Quisquam, quae.</p>
              <p style="width: 600px">Lorem ipsum dolor sit amet, consectetur adipisicing elit. Et dignissimos quaerat fugiat hic, fugit enim totam nemo blanditiis minus quae?</p>
            </div>
            <img src="" alt="">
              <button>Click me</button>
          </div>
        </section> <!-- Back -->
        <section></section> <!-- Third / Middle -->
        <section></section> <!-- Seond to front -->
        <section></section> <!-- Front --> 
    </main>
</body>
</html>